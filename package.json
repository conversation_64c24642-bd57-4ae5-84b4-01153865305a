{"name": "real-estate-acquisition-automation", "version": "1.0.0", "private": true, "description": "Comprehensive Next.js web application for automating real estate land acquisition sourcing", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prebuild": "npm run type-check && npm run lint", "postinstall": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.7.4", "@googlemaps/google-maps-services-js": "^3.4.0", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.1.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.62.7", "@tanstack/react-table": "^8.20.5", "bcryptjs": "^2.4.3", "chart.js": "^4.4.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "d3": "^7.9.0", "date-fns": "^4.1.0", "framer-motion": "^11.15.0", "jspdf": "^2.5.2", "lucide-react": "^0.468.0", "next": "^14.2.18", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "openai": "^4.77.3", "pdf-lib": "^1.17.1", "pdfmake": "^0.2.12", "playwright": "^1.49.1", "puppeteer": "^23.10.4", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "recharts": "^2.13.3", "sharp": "^0.33.5", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/d3": "^7.4.3", "@types/google.maps": "^3.58.1", "@types/jest": "^29.5.14", "@types/node": "^24.0.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "^14.2.18", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "postcss": "^8.4.35", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "prisma": "^6.1.0", "tailwindcss": "^3.4.17", "tsx": "^4.19.2", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}