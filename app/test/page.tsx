"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CheckCircle, AlertCircle, Info } from "lucide-react";

export default function TestPage() {
  const [inputValue, setInputValue] = useState("");
  const [selectValue, setSelectValue] = useState("");
  const [showAlert, setShowAlert] = useState(false);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Component Test Page
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Testing all UI components to ensure they work correctly
          </p>
        </div>

        {/* Buttons Test */}
        <Card>
          <CardHeader>
            <CardTitle>Button Components</CardTitle>
            <CardDescription>Testing different button variants and sizes</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Button>Default</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="link">Link</Button>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button size="sm">Small</Button>
              <Button size="default">Default</Button>
              <Button size="lg">Large</Button>
              <Button size="icon">
                <CheckCircle className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Form Components Test */}
        <Card>
          <CardHeader>
            <CardTitle>Form Components</CardTitle>
            <CardDescription>Testing input, label, and select components</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="test-input">Test Input</Label>
                <Input
                  id="test-input"
                  placeholder="Enter some text..."
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="test-select">Test Select</Label>
                <Select value={selectValue} onValueChange={setSelectValue}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="option1">Option 1</SelectItem>
                    <SelectItem value="option2">Option 2</SelectItem>
                    <SelectItem value="option3">Option 3</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Input value: {inputValue || "None"}
              <br />
              Select value: {selectValue || "None"}
            </div>
          </CardContent>
        </Card>

        {/* Badges Test */}
        <Card>
          <CardHeader>
            <CardTitle>Badge Components</CardTitle>
            <CardDescription>Testing different badge variants</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Badge>Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="destructive">Destructive</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Alerts Test */}
        <Card>
          <CardHeader>
            <CardTitle>Alert Components</CardTitle>
            <CardDescription>Testing alert component</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={() => setShowAlert(!showAlert)}>
              {showAlert ? "Hide" : "Show"} Alert
            </Button>
            {showAlert && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  This is a test alert to verify the component is working correctly.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Theme Test */}
        <Card>
          <CardHeader>
            <CardTitle>Theme Test</CardTitle>
            <CardDescription>Testing dark/light theme compatibility</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-background border rounded-lg">
                <h4 className="font-medium text-foreground mb-2">Background Colors</h4>
                <div className="space-y-2 text-sm">
                  <div className="p-2 bg-primary text-primary-foreground rounded">Primary</div>
                  <div className="p-2 bg-secondary text-secondary-foreground rounded">Secondary</div>
                  <div className="p-2 bg-muted text-muted-foreground rounded">Muted</div>
                  <div className="p-2 bg-accent text-accent-foreground rounded">Accent</div>
                </div>
              </div>
              <div className="p-4 bg-card border rounded-lg">
                <h4 className="font-medium text-card-foreground mb-2">Card Colors</h4>
                <div className="space-y-2 text-sm">
                  <div className="text-foreground">Foreground text</div>
                  <div className="text-muted-foreground">Muted foreground text</div>
                  <div className="p-2 border border-border rounded">Border example</div>
                  <div className="p-2 border border-input rounded">Input border</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status Test */}
        <Card>
          <CardHeader>
            <CardTitle>Component Status</CardTitle>
            <CardDescription>Overall component health check</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm">Buttons working</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm">Forms working</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm">Cards working</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm">Badges working</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm">Alerts working</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm">Theme working</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
